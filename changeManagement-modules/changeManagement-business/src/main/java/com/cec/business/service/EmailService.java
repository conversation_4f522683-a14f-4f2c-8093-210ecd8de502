package com.cec.business.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cec.business.config.constant.MailConstant;
import com.cec.business.domain.ApplicationManage;
import com.cec.business.domain.ChangeDelay;
import com.cec.business.domain.ChangeInfo;
import com.cec.business.domain.ChangeItem;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.NetworkFreezeInfo;
import com.cec.business.domain.NetworkFreezeItem;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.domain.vo.ApplicationManageVo;
import com.cec.business.domain.vo.ChangeDelayVo;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.domain.vo.FreezeAreaVo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.mapper.ApplicationManageMapper;
import com.cec.business.mapper.ChangeDelayMapper;
import com.cec.business.mapper.ChangeInfoMapper;
import com.cec.business.mapper.ChangeItemMapper;
import com.cec.business.mapper.NetworkFreezeAreaMapper;
import com.cec.business.mapper.NetworkFreezeInfoMapper;
import com.cec.business.mapper.NetworkFreezeItemMapper;
import com.cec.common.core.constant.GlobalConstants;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.mybatis.helper.DataPermissionHelper;
import com.cec.common.redis.utils.RedisUtils;
import com.cec.common.mail.config.properties.MailProperties;
import com.cec.common.mail.utils.MailUtils;
import com.cec.common.oss.core.OssClient;
import com.cec.common.oss.factory.OssFactory;
import com.cec.system.domain.SysDept;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.vo.SysOssVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.domain.vo.UserVo;
import com.cec.system.mapper.SysDeptMapper;
import com.cec.system.mapper.SysUserMapper;
import com.cec.system.service.ISysOssService;
import com.cec.system.service.ISysUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;

@Service
@Slf4j
public class EmailService {

    // 需要发送邮件的变更阶段列表
    private static final List<ChangeStageEnum> EMAIL_STAGES = List.of(
        ChangeStageEnum.IMPLEMENTING,
        ChangeStageEnum.COMPLETED,
        ChangeStageEnum.ROLLED_BACK,
        ChangeStageEnum.CANCELLED,
        ChangeStageEnum.DELAYED,
        ChangeStageEnum.OVER_7_DAYS
    );

    @Resource
    private ISysOssService sysOssService;
    @Resource
    private ChangeInfoMapper changeInfoMapper;
    @Resource
    private ChangeItemMapper changeItemMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private NetworkFreezeInfoMapper networkFreezeInfoMapper;
    @Resource
    private NetworkFreezeItemMapper networkFreezeItemMapper;
    @Resource
    private NetworkFreezeAreaMapper networkFreezeAreaMapper;
    @Resource
    private ChangeDelayMapper changeDelayMapper;
    @Resource
    private ApplicationManageMapper applicationManageMapper;
    @Resource
    private MailProperties mailProperties;
    @Resource
    private SysUserMapper sysUserMapper;

    @Value("${host:http://localhost:10086}")
    private String host;

    /**
     * 替换邮件模板中的占位符并发送邮件到指定QQ邮箱
     *
     * @param stage          阶段
     * @param placeholderMap 占位符替换映射
     * @param to             收件人邮箱地址
     * @param cc             抄送人邮箱地址，可以为null
     * @param bcc            密送人邮箱地址，可以为null
     * @param files          附件列表
     * @return 邮件发送结果的message-id
     */
    public String sendTemplateEmail(String stage, Map<String, String> placeholderMap,
                                    String to, String cc, String bcc, File... files) {
        // 获取对应的邮件主题和内容模板
        String subjectTemplate = getSubjectTemplate(stage);
        String contentTemplate = getContentTemplate(stage);

        // 替换占位符
        String subject = replacePlaceholders(subjectTemplate, placeholderMap);
        String content = replacePlaceholders(contentTemplate, placeholderMap);

        // 处理固定收件人配置
        String[] recipients = processRecipients(to, cc);
        String finalTo = recipients[0];
        String finalCc = recipients[1];

        // 检查抄送和密送参数，避免传入空字符串
        finalCc = StrUtil.isBlank(finalCc) ? null : finalCc;
        bcc = StrUtil.isBlank(bcc) ? null : bcc;

        // 发送邮件（HTML格式）
        log.info("发送邮件，收件人：{}，抄送：{}，密送：{}，主题：{}", finalTo, finalCc, bcc, subject);

        // 使用支持抄送和密送的邮件发送方法
        if (files != null && files.length > 0) {
            return MailUtils.send(finalTo, finalCc, bcc, subject, content, true, files);
        } else {
            return MailUtils.send(finalTo, finalCc, bcc, subject, content, true);
        }
    }

    /**
     * 发送带OSS附件的邮件，支持传入发送人、抄送人、邮件正文和ossIds
     * 通过sysOssService处理ossIds获取附件文件
     *
     * @param to      收件人邮箱地址
     * @param cc      抄送人邮箱地址，可以为null
     * @param subject 邮件主题
     * @param content 邮件正文内容
     * @param isHtml  是否为HTML格式
     * @param ossIds  OSS文件ID列表，每个ID为Long类型
     * @return 邮件发送结果的message-id
     */
    public String sendEmailWithOssAttachments(String to, String cc, String subject, String content,
                                              boolean isHtml, List<Long> ossIds) {
        log.info("发送带OSS附件的邮件，收件人：{}，抄送：{}，主题：{}, 附件IDs：{}", to, cc, subject, ossIds);

        // 处理固定收件人配置
        String[] recipients = processRecipients(to, cc);
        String finalTo = recipients[0];
        String finalCc = recipients[1];

        // 检查抄送参数，避免传入空字符串
        finalCc = StrUtil.isBlank(finalCc) ? null : finalCc;

        // 准备附件
        File[] files = null;
        List<File> tempFiles = new ArrayList<>();

        if (CollUtil.isNotEmpty(ossIds)) {
            try {
                // 获取所有OSS对象信息
                List<SysOssVo> ossList = sysOssService.listByIds(ossIds);
                log.info("找到附件数量: {}", ossList.size());

                if (CollUtil.isNotEmpty(ossList)) {
                    // 创建临时文件列表
                    List<File> finalFiles = new ArrayList<>();
                    int fileIndex = 1;

                    // 从OSS下载文件到临时目录
                    for (SysOssVo ossVo : ossList) {
                        try {
                            log.info("处理附件: {}, ossId: {}", ossVo.getOriginalName(), ossVo.getOssId());

                            // 使用固定前缀创建临时文件
                            String tmpPrefix = "att" + fileIndex;
                            File tempFile = File.createTempFile(tmpPrefix, ".tmp");
                            tempFile.deleteOnExit(); // 确保程序退出时删除临时文件
                            tempFiles.add(tempFile); // 添加到列表以便清理

                            // 从OSS下载文件内容到临时文件
                            ByteArrayOutputStream baos = new ByteArrayOutputStream();
                            OssClient storage = OssFactory.instance(ossVo.getService());
                            storage.download(ossVo.getFileName(), baos, contentLength -> {
                                // 不需要处理内容长度
                            });

                            // 写入内容到临时文件
                            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                                fos.write(baos.toByteArray());
                            }

                            // 使用源文件名创建最终文件
                            File finalFile = new File(tempFile.getParent(), ossVo.getOriginalName());
                            if (finalFile.exists()) {
                                finalFile.delete(); // 如果文件已存在，先删除
                            }
                            boolean renamed = tempFile.renameTo(finalFile);

                            if (renamed) {
                                finalFiles.add(finalFile);
                                tempFiles.add(finalFile);
                                log.info("成功创建附件文件: {}, 路径: {}", finalFile.getName(), finalFile.getAbsolutePath());
                            } else {
                                log.error("重命名文件失败，将使用临时文件: {}", tempFile.getAbsolutePath());
                                finalFiles.add(tempFile);
                            }

                            fileIndex++;
                        } catch (Exception e) {
                            log.error("处理OSS文件失败: {}", ossVo.getOriginalName(), e);
                        }
                    }

                    if (!finalFiles.isEmpty()) {
                        files = finalFiles.toArray(new File[0]);
                    }
                }
            } catch (Exception e) {
                log.error("处理OSS文件失败", e);
            }
        }

        // 发送邮件
        try {
            String messageId;
            if (files != null && files.length > 0) {
                log.info("开始发送带附件的邮件，附件数量: {}", files.length);
                for (File file : files) {
                    log.info("附件: {}", file.getName());
                }
                messageId = MailUtils.send(finalTo, finalCc, null, subject, content, isHtml, files);
            } else {
                log.info("发送不带附件的邮件");
                messageId = MailUtils.send(finalTo, finalCc, null, subject, content, isHtml);
            }
            return messageId;
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            throw new RuntimeException("发送邮件失败: " + e.getMessage(), e);
        } finally {
            // 确保清理临时文件
            if (!tempFiles.isEmpty()) {
                for (File file : tempFiles) {
                    try {
                        if (file != null && file.exists()) {
                            file.delete();
                            log.debug("已清理临时文件: {}", file.getAbsolutePath());
                        }
                    } catch (Exception e) {
                        log.warn("删除临时文件失败: {}", file.getName(), e);
                    }
                }
            }
        }
    }

    /**
     * 仅替换模板中的占位符并返回结果，不发送邮件（用于测试）
     *
     * @param stage          邮件模板类型
     * @param placeholderMap 占位符替换映射
     * @return 替换后的主题和内容
     */
    public Map<String, String> processEmailTemplate(String stage, Map<String, String> placeholderMap) {
        String subjectTemplate = getSubjectTemplate(stage);
        String contentTemplate = getContentTemplate(stage);

        String subject = replacePlaceholders(subjectTemplate, placeholderMap);
        String content = replacePlaceholders(contentTemplate, placeholderMap);

        return Map.of(
            "subject", subject,
            "content", content
        );
    }

    /**
     * 替换模板中的占位符
     *
     * @param template       模板字符串
     * @param placeholderMap 占位符替换映射
     * @return 替换后的字符串
     */
    private String replacePlaceholders(String template, Map<String, String> placeholderMap) {
        String result = template;
        for (Map.Entry<String, String> entry : placeholderMap.entrySet()) {
            result = StrUtil.replace(result, "{$" + entry.getKey() + "}", entry.getValue());
        }
        return result;
    }

    /**
     * 获取邮件主题模板
     *
     * @param stage 邮件模板类型
     * @return 邮件主题模板
     */
    private String getSubjectTemplate(String stage) {
        return switch (stage) {
            case "IMPLEMENTING" -> MailConstant.CHANGE_START_SUBJECT;
            case "IMPLEMENTING_CN" -> MailConstant.CHANGE_START_SUBJECT_CN;
            case "COMPLETED", "ROLLED_BACK" -> MailConstant.CHANGE_END_SUBJECT;
            case "COMPLETED_CN", "ROLLED_BACK_CN" -> MailConstant.CHANGE_END_SUBJECT_CN;
            case "DELAYED" -> MailConstant.CHANGE_DELAY_SUBJECT;
            case "DELAYED_CN" -> MailConstant.CHANGE_DELAY_SUBJECT_CN;
            case "CANCELLED" -> MailConstant.CHANGE_CANCEL_SUBJECT;
            case "CANCELLED_CN" -> MailConstant.CHANGE_CANCEL_SUBJECT_CN;
            case "APPROVED" -> MailConstant.CHANGE_APPROVED_SUBJECT;
            case "APPROVED_CN" -> MailConstant.CHANGE_APPROVED_SUBJECT_CN;
            case "OVER_7_DAYS", "OVER_7_DAYS_CN" -> MailConstant.CHANGE_OVER_7_DAYS_SUBJECT;

            default -> throw new IllegalArgumentException("Invalid stage: " + stage);
        };
    }

    /**
     * 获取邮件内容模板
     *
     * @param stage 邮件模板类型
     * @return 邮件内容模板
     */
    private String getContentTemplate(String stage) {
        return switch (stage) {
            case "IMPLEMENTING" -> MailConstant.CHANGE_START_CONTENT;
            case "IMPLEMENTING_CN" -> MailConstant.CHANGE_START_CONTENT_CN;
            case "COMPLETED", "ROLLED_BACK" -> MailConstant.CHANGE_END_CONTENT;
            case "COMPLETED_CN", "ROLLED_BACK_CN" -> MailConstant.CHANGE_END_CONTENT_CN;
            case "DELAYED" -> MailConstant.CHANGE_DELAY_CONTENT;
            case "DELAYED_CN" -> MailConstant.CHANGE_DELAY_CONTENT_CN;
            case "CANCELLED" -> MailConstant.CHANGE_CANCEL_CONTENT;
            case "CANCELLED_CN" -> MailConstant.CHANGE_CANCEL_CONTENT_CN;
            case "OVER_7_DAYS", "OVER_7_DAYS_CN" -> MailConstant.CHANGE_OVER_7_DAYS_CONTENT;
            default -> throw new IllegalArgumentException("Invalid stage: " + stage);
        };
    }

    /**
     * 处理固定收件人配置
     * 如果启用了固定收件人开关，则使用固定收件人；否则使用原始收件人
     */
    private String[] processRecipients(String originalTo, String originalCc) {
        // 如果未启用固定收件人开关，直接返回原始收件人
        if (!Boolean.TRUE.equals(mailProperties.getUseFixedRecipients())) {
            return new String[]{originalTo, originalCc};
        }

        String finalTo = StrUtil.isNotBlank(mailProperties.getFixedTo()) ? mailProperties.getFixedTo() : originalTo;
        String finalCc = StrUtil.isNotBlank(mailProperties.getFixedCc()) ? mailProperties.getFixedCc() : originalCc;

        if (!finalTo.equals(originalTo) || !finalCc.equals(originalCc)) {
            log.info("启用固定收件人模式 - 原收件人: {}, 原抄送: {} -> 固定收件人: {}, 固定抄送: {}",
                originalTo, originalCc, finalTo, finalCc);
        }

        return new String[]{finalTo, finalCc};
    }

    /**
     * 根据变更ID和阶段发送邮件
     *
     * @param stage        变更阶段枚举
     * @param changeInfoId 变更信息ID
     */
    public void sendEmailByChangeInfoId(ChangeStageEnum stage, Long changeInfoId) {
        try {
            log.info("准备根据变更ID发送邮件, 变更ID: {}, 阶段: {}", changeInfoId, stage);

            // 基础数据查询
            ChangeInfo info = changeInfoMapper.selectById(changeInfoId);
            if (info == null) {
                log.error("发送变更邮件失败, 变更信息不存在, 变更ID: {}", changeInfoId);
                throw new RuntimeException("变更信息不存在");
            }

            ChangeItemVo itemVo = changeItemMapper.selectVoOne(new LambdaQueryWrapper<ChangeItem>().eq(ChangeItem::getChangeId, changeInfoId));
            if (itemVo == null) {
                log.error("发送变更邮件失败, 变更项信息不存在, 变更ID: {}", changeInfoId);
                throw new RuntimeException("变更项信息不存在");
            }

            // 获取收件人和抄送人
            String to = CollUtil.join(info.getNotificationEmail(), ";");
            String cc = CollUtil.join(info.getNotificationCcEmail(), ";");

            // 处理需要发送邮件的阶段
            if (EMAIL_STAGES.contains(stage)) {
                // 检查收件人
                if (StrUtil.isBlank(to)) {
                    log.warn("变更申请人邮箱为空，无法发送邮件, 变更ID: {}", changeInfoId);
                    return;
                }

                // 邮件去重检查
                String deduplicationKey = buildDeduplicationKey(stage, changeInfoId);
                if (deduplicationKey != null && RedisUtils.getCacheObject(deduplicationKey) != null) {
                    log.info("{}阶段邮件已发送过，跳过重复发送。变更ID: {}", stage, changeInfoId);
                    return;
                }

                // OVER_7_DAYS阶段的特殊处理
                if (ChangeStageEnum.OVER_7_DAYS.equals(stage)) {
                    handleOver7DaysStage(stage, changeInfoId, info, itemVo);
                    return;
                }

                // 构建邮件占位符映射
                Map<String, String> placeholderMap = buildPlaceholderMap(info, itemVo, changeInfoId);

                // 发送邮件
                log.info("开始发送变更邮件, 变更ID: {}, 收件人: {}, 抄送: {}", changeInfoId, to, cc);
                String newStage = getNewStage(stage, info);
                sendTemplateEmail(newStage, placeholderMap, to, cc, null);

                // 设置去重标记
                setDeduplicationFlag(stage, changeInfoId);


            } else if (ChangeStageEnum.APPROVED.equals(stage)) {
                handleApprovedStage(info, itemVo, to, cc);
            }
        } catch (Exception e) {
            log.error("发送变更邮件失败, 变更ID: {}", changeInfoId, e);
            throw new RuntimeException("发送变更邮件失败", e);
        }
    }

    /**
     * 构建去重键
     *
     * @param stage        变更阶段
     * @param changeInfoId 变更ID
     * @return 去重键，如果不需要去重则返回null
     */
    private String buildDeduplicationKey(ChangeStageEnum stage, Long changeInfoId) {
        if (ChangeStageEnum.IMPLEMENTING.equals(stage)) {
            return GlobalConstants.EMAIL_DEDUPLICATION_KEY + "implementing:" + changeInfoId;
        } else if (ChangeStageEnum.OVER_7_DAYS.equals(stage)) {
            return GlobalConstants.EMAIL_DEDUPLICATION_KEY + "over7days:" + changeInfoId;
        }
        return null;
    }

    /**
     * 设置去重标记
     *
     * @param stage        变更阶段
     * @param changeInfoId 变更ID
     */
    private void setDeduplicationFlag(ChangeStageEnum stage, Long changeInfoId) {
        if (ChangeStageEnum.IMPLEMENTING.equals(stage)) {
            String deduplicationKey = GlobalConstants.EMAIL_DEDUPLICATION_KEY + "implementing:" + changeInfoId;
            RedisUtils.setCacheObject(deduplicationKey, "1", Duration.ofDays(30));
            log.info("IMPLEMENTING阶段邮件发送成功，设置去重标记，变更ID: {}", changeInfoId);
        } else if (ChangeStageEnum.OVER_7_DAYS.equals(stage)) {
            String deduplicationKey = GlobalConstants.EMAIL_DEDUPLICATION_KEY + "over7days:" + changeInfoId;
            RedisUtils.setCacheObject(deduplicationKey, "1");
            log.info("OVER_7_DAYS阶段邮件发送成功，设置去重标记，变更ID: {}", changeInfoId);
        }
    }

    /**
     * 构建邮件占位符映射
     *
     * @param info         变更信息
     * @param itemVo       变更项目信息
     * @param changeInfoId 变更ID
     * @return 占位符映射
     */
    private Map<String, String> buildPlaceholderMap(ChangeInfo info, ChangeItemVo itemVo, Long changeInfoId) {
        SysDept sysDept = sysDeptMapper.selectById(info.getTeamId());
        Map<String, String> placeholderMap = new HashMap<>();

        // 基础信息
        placeholderMap.put("Emergency/Scheduled", WhetherEnum.YES.equals(info.getIsUrgentChange()) ? "Emergency" : "Scheduled");
        placeholderMap.put("SystemName", CollUtil.join(info.getApplicationName(), ","));
        placeholderMap.put("StartDate", DateUtil.format(info.getPlanTimeStart(), "yyyy-MM-dd HH:mm:ss"));
        placeholderMap.put("EndDate", DateUtil.format(info.getPlanTimeEnd(), "yyyy-MM-dd HH:mm:ss"));
        placeholderMap.put("ChangeNumberURL", getChangeURL(null, itemVo.getChangeCode(), itemVo.getChangeId()));
        placeholderMap.put("ChangeNumber", itemVo.getChangeCode());
        placeholderMap.put("Requestor", String.format("%s(%s)", info.getRequester().getStaffName(), info.getRequester().getStaffId()));
        placeholderMap.put("Current Date", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        placeholderMap.put("TeamName", sysDept.getEmailSign());
        placeholderMap.put("紧急/常规", WhetherEnum.YES.equals(info.getIsUrgentChange()) ? "紧急" : "常规");

        // 延期信息处理
        if (Objects.nonNull(info.getDelayEndTime())) {
            placeholderMap.put("EndDate", DateUtil.format(info.getDelayEndTime(), "yyyy-MM-dd HH:mm:ss"));
            placeholderMap.put("NewEndDate", DateUtil.format(info.getDelayEndTime(), "yyyy-MM-dd HH:mm:ss"));

            Optional<List<ChangeDelayVo>> changeDelayVos = Optional.ofNullable(
                changeDelayMapper.selectVoList(new LambdaQueryWrapper<ChangeDelay>().eq(ChangeDelay::getChangeId, changeInfoId))
            );
            changeDelayVos.ifPresent(delays -> delays.stream()
                .max(Comparator.comparing(ChangeDelayVo::getId))
                .ifPresent(latestDelay -> placeholderMap.put("ExtensionReason", latestDelay.getRemark()))
            );
        }

        return placeholderMap;
    }

    /**
     * 处理OVER_7_DAYS阶段的特殊逻辑
     *
     * @param stage        变更阶段
     * @param changeInfoId 变更ID
     * @param info         变更信息
     * @param itemVo       变更项目信息
     */
    private void handleOver7DaysStage(ChangeStageEnum stage, Long changeInfoId, ChangeInfo info, ChangeItemVo itemVo) {
        // 获取用户信息 - 忽略数据权限检查，因为这是在非web上下文的定时任务中执行
        SysUserVo toVo = DataPermissionHelper.ignore(() ->
            sysUserMapper.selectVoById(info.getRequester().getUserId()));
        List<SysUserVo> ccVos = DataPermissionHelper.ignore(() ->
            sysUserMapper.selectUserList(new LambdaQueryWrapper<SysUser>()
                .in(SysUser::getUserId, info.getTeamLeaderList().stream().map(UserVo::getUserId).toList())));

        // 构建占位符映射
        Map<String, String> placeholderMap = buildPlaceholderMap(info, itemVo, changeInfoId);

        // 发送邮件
        String toEmail = toVo.getEmail();
        String ccEmails = ccVos.stream().map(SysUserVo::getEmail).collect(Collectors.joining(";"));

        log.info("开始发送OVER_7_DAYS变更邮件, 变更ID: {}, 收件人: {}, 抄送: {}", changeInfoId, toEmail, ccEmails);
        String newStage = getNewStage(stage, info);
        sendTemplateEmail(newStage, placeholderMap, toEmail, ccEmails, null);

        // 设置去重标记
        setDeduplicationFlag(stage, changeInfoId);
    }

    /**
     * 处理APPROVED阶段的邮件发送
     *
     * @param info   变更信息
     * @param itemVo 变更项目信息
     * @param to     收件人
     * @param cc     抄送人
     */
    private void handleApprovedStage(ChangeInfo info, ChangeItemVo itemVo, String to, String cc) {
        // 检查计划开始时间
        Date currentTime = new Date();
        Date planStartTime = info.getPlanTimeStart();

        if (planStartTime != null && currentTime.after(planStartTime)) {
            log.info("当前时间已过计划开始时间，跳过邮件发送。变更ID: {}, 当前时间: {}, 计划开始时间: {}",
                info.getId(),
                DateUtil.format(currentTime, "yyyy-MM-dd HH:mm:ss"),
                DateUtil.format(planStartTime, "yyyy-MM-dd HH:mm:ss"));
            return;
        }

        // 构建占位符映射
        Map<String, String> placeholderMap = new HashMap<>();
        placeholderMap.put("Emergency/Scheduled", WhetherEnum.YES.equals(info.getIsUrgentChange()) ? "Emergency" : "Scheduled");
        placeholderMap.put("SystemName", CollUtil.join(info.getApplicationName(), ","));
        placeholderMap.put("StartDate", DateUtil.format(info.getPlanTimeStart(), "yyyy-MM-dd HH:mm"));
        placeholderMap.put("EndDate", DateUtil.format(Objects.nonNull(info.getDelayEndTime()) ? info.getDelayEndTime() : info.getPlanTimeEnd(), "yyyy-MM-dd HH:mm"));
        placeholderMap.put("ChangeNumberURL", getChangeURL(null, itemVo.getChangeCode(), itemVo.getChangeId()));
        placeholderMap.put("ChangeNumber", itemVo.getChangeCode());
        placeholderMap.put("Requestor", String.format("%s(%s)", info.getRequester().getStaffName(), info.getRequester().getStaffId()));
        placeholderMap.put("Current Date", DateUtil.format(currentTime, "yyyy-MM-dd HH:mm:ss"));
        placeholderMap.put("紧急/常规", WhetherEnum.YES.equals(info.getIsUrgentChange()) ? "紧急" : "常规");

        // 构建邮件内容
        String newStage = getNewStage(ChangeStageEnum.APPROVED, info);
        String subjectTemplate = getSubjectTemplate(newStage);
        String subject = replacePlaceholders(subjectTemplate, placeholderMap);
        String content = replacePlaceholders(info.getEmailContent(), placeholderMap);

        // 处理收件人并发送邮件
        String[] recipients = processRecipients(to, cc);
        String finalTo = recipients[0];
        String finalCc = recipients[1];

        MailUtils.send(finalTo, finalCc, null, subject, content, true);
    }

    /**
     * 判断发中文还是英文版邮件
     *
     * @param stage 阶段
     * @return 邮件主题模板
     */
    private String getNewStage(ChangeStageEnum stage, ChangeInfo info) {
        // 早期返回：如果stage为null，直接返回空字符串
        if (stage == null) {
            return "";
        }

        List<String> appIds = info.getApplicationIds();
        // 早期返回：如果应用ID列表为空，直接返回stage名称
        if (CollUtil.isEmpty(appIds)) {
            return stage.name();
        }

        // 查询应用管理信息
        List<ApplicationManageVo> manageVos = applicationManageMapper.selectVoList(
            new LambdaQueryWrapper<ApplicationManage>().in(ApplicationManage::getId, appIds)
        );

        // 早期返回：如果查询结果为空，直接返回stage名称
        if (CollUtil.isEmpty(manageVos)) {
            return stage.name();
        }

        // 常量定义，提高可维护性
        final String MAINLAND_AREA = "MAINLAND";
        final String CN_SUFFIX = "_CN";

        // 提取所有区域名称，合并中英文区域名称并转换为大写去重
        Set<String> allAreaNames = manageVos.stream()
            .filter(app -> CollUtil.isNotEmpty(app.getFreezeAreaList()))
            .flatMap(app -> app.getFreezeAreaList().stream())
            .flatMap(area -> {
                // 合并中文和英文区域名称
                Stream<String> cnNames = Optional.ofNullable(area.getAreaName()).stream();
                Stream<String> enNames = Optional.ofNullable(area.getAreaNameEn()).stream();
                return Stream.concat(cnNames, enNames);
            })
            .filter(Objects::nonNull)
            .filter(name -> !name.trim().isEmpty())
            .map(name -> name.trim().toUpperCase(java.util.Locale.ENGLISH))
            .collect(Collectors.toSet());

        // 判断是否只有一个区域且为mainland（忽略大小写）
        if (allAreaNames.size() == 1 && allAreaNames.contains(MAINLAND_AREA)) {
            return stage.name() + CN_SUFFIX;
        }

        return stage.name();
    }

    /**
     * 根据封网ID和发送邮件
     *
     * @param freezeInfoId 变更信息ID
     */
    public void sendEmailByFreezeId(Long freezeInfoId) {
        try {
            log.info("发送封网邮件, ID: {}", freezeInfoId);

            NetworkFreezeInfo info = networkFreezeInfoMapper.selectById(freezeInfoId);
            NetworkFreezeItem item = networkFreezeItemMapper.selectOne(new LambdaQueryWrapper<NetworkFreezeItem>().eq(NetworkFreezeItem::getFreezeId, freezeInfoId));

            List<NetworkFreezeArea> networkFreezeAreas = networkFreezeAreaMapper.selectList(new LambdaQueryWrapper<NetworkFreezeArea>().eq(NetworkFreezeArea::getFreezeId, freezeInfoId));

            // 处理FrozenPeriodandArea字段，格式化区域和时间信息
            StringBuilder periodAndAreaBuilder = new StringBuilder();
            StringBuilder periodBuilderCn = new StringBuilder();
            StringBuilder periodBuilderEn = new StringBuilder();
            if (CollUtil.isNotEmpty(networkFreezeAreas)) {
                for (int i = 0; i < networkFreezeAreas.size(); i++) {
                    NetworkFreezeArea area = networkFreezeAreas.get(i);
                    // 格式化为: 区域名称: 开始时间 - 结束时间
                    periodAndAreaBuilder.append(CollUtil.join(area.getAreaName(), ","))
                        .append(": ")
                        .append(DateUtil.format(area.getStartTime(), "yyyy-MM-dd HH:mm:ss"))
                        .append(" - ")
                        .append(DateUtil.format(area.getEndTime(), "yyyy-MM-dd HH:mm:ss"));

                    if (i < networkFreezeAreas.size() - 1) {
                        periodAndAreaBuilder.append("; ");
                    }
                }
            } else {
                periodAndAreaBuilder.append("No frozen areas defined");
            }

            periodBuilderCn.append(DateUtil.format(item.getFreezeStartTime(), "yyyy年MM月dd日 HH:mm:ss"))
                .append("~")
                .append(DateUtil.format(item.getFreezeEndTime(), "yyyy年MM月dd日 HH:mm:ss"));

            periodBuilderEn.append(DateUtil.format(item.getFreezeStartTime(), "yyyy/MM/dd HH:mm:ss"))
                .append("~")
                .append(DateUtil.format(item.getFreezeEndTime(), "yyyy/MM/dd HH:mm:ss"));

            // 获取变更信息
            Map<String, String> placeholderMap = new HashMap<>();
            placeholderMap.put("NetworkFreezeLevel", info.getLevel().getInfo());
            placeholderMap.put("NetworkFreezeLevelCN", info.getLevel().getInfoCn());
            placeholderMap.put("FrozenPeriodandArea", periodAndAreaBuilder.toString());
            placeholderMap.put("FrozenPeriodCn", periodBuilderCn.toString());
            placeholderMap.put("FrozenPeriodEn", periodBuilderEn.toString());
            placeholderMap.put("NetworkFreezeNo", item.getFreezeCode());

            String subject = replacePlaceholders(MailConstant.FREEZE_SUBJECT, placeholderMap);
            String content = info.getEmailContent();
            String to = CollUtil.join(JsonUtils.parseArray(info.getNotificationEmailTo(), String.class), ";");
            String cc = CollUtil.join(JsonUtils.parseArray(info.getNotificationEmailCc(), String.class), ";");

            // MailUtils.send(to, cc, null, subject, content, true);

            if (WhetherEnum.YES.equals(info.getIsAttachment())) {
                sendEmailWithOssAttachments(to, cc, subject, content, true, info.getFileIds());
            } else {
                // 处理固定收件人配置
                String[] recipients = processRecipients(to, cc);
                String finalTo = recipients[0];
                String finalCc = recipients[1];

                MailUtils.send(finalTo, finalCc, null, subject, content, true);
            }

        } catch (Exception e) {
            log.error("发送封网邮件失败, 封网ID: {}", freezeInfoId, e);
            throw new RuntimeException("发送封网邮件失败", e);
        }
    }

    /**
     * 发送下一节点邮件变更
     *
     * @param changeRecordId recordId
     * @param changeInfoId   变更信息ID
     * @param userId         用户ID
     */
    public void sendApprovalChangeEmail(Long changeRecordId, Long changeInfoId, Long userId) {
        try {
            // 获取变更信息
            ChangeInfo info = changeInfoMapper.selectById(changeInfoId);
            if (info == null) {
                log.error("发送变更邮件失败, 变更信息不存在, 变更ID: {}, UserID: {}", changeInfoId, userId);
                throw new RuntimeException("变更信息不存在");
            }

            ChangeItem itemVo = changeItemMapper.selectOne(new LambdaQueryWrapper<ChangeItem>().eq(ChangeItem::getChangeId, changeInfoId));
            if (itemVo == null) {
                log.error("发送变更邮件失败, 变更项信息不存在, 变更ID: {}, UserID: {}", changeInfoId, userId);
                throw new RuntimeException("变更项信息不存在");
            }

            if (StringUtils.isBlank(itemVo.getChangeCode())) {
                log.error("发送变更邮件失败, 变更项信息不存在变更编号, 变更ID: {}, UserID: {}", changeInfoId, userId);
                throw new RuntimeException("变更项信息不存在变更编号");
            }

            // 获取收件人和抄送人
            String to = getUserMail(userId);
            String cc = getUserMail(info.getRequester().getUserId());

            // 检查邮箱
            if (StringUtils.isBlank(to)) {
                log.warn("收件人邮箱为空，无法发送邮件, 变更ID: {}, UserID: {}", changeInfoId, userId);
                return;
            }

            // 获取变更信息
            Map<String, String> placeholderMap = new HashMap<>();
            placeholderMap.put("Emergency/Scheduled", WhetherEnum.YES.equals(info.getIsUrgentChange()) ? "Emergency" : "Scheduled");
            placeholderMap.put("ChangeNumberURL", getChangeURL(changeRecordId, itemVo.getChangeCode(), itemVo.getChangeId()));
            placeholderMap.put("ChangeNumber", itemVo.getChangeCode());
            placeholderMap.put("Requestor", String.format("%s(%s)", info.getRequester().getStaffName(), info.getRequester().getStaffId()));
            placeholderMap.put("Current Date", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));


            String subject = replacePlaceholders(MailConstant.CHANGE_TODO_SUBJECT, placeholderMap);
            String content = replacePlaceholders(MailConstant.CHANGE_TODO_CONTENT, placeholderMap);

            // 处理固定收件人配置
            String[] recipients = processRecipients(to, cc);
            String finalTo = recipients[0];
            String finalCc = recipients[1];

            MailUtils.send(finalTo, finalCc, null, subject, content, true);

        } catch (Exception e) {
            log.error("发送变更邮件失败, 变更ID: {}, UserID: {}", changeInfoId, userId, e);
            throw new RuntimeException("发送变更邮件失败", e);
        }
    }

    /**
     * 发送下一节点邮件封网
     *
     * @param freezeInfoId 封网信息ID
     * @param userId       用户ID
     */
    public void sendApprovalFreezeEmail(Long freezeRecordId, Long freezeInfoId, Long userId) {
        try {

            NetworkFreezeInfo info = networkFreezeInfoMapper.selectById(freezeInfoId);
            NetworkFreezeItem itemVo = networkFreezeItemMapper.selectOne(new LambdaQueryWrapper<NetworkFreezeItem>().eq(NetworkFreezeItem::getFreezeId, freezeInfoId));
            // 获取收件人和抄送人
            String to = getUserMail(userId);
            String cc = getUserMail(info.getRequester().getUserId());

            if (itemVo == null || StrUtil.isBlank(itemVo.getFreezeCode())) {
                log.error("发送封网邮件失败, 封网信息不存在, 封网ID: {}, UserID: {}", freezeInfoId, userId);
                throw new RuntimeException("封网信息不存在");
            }

            // 获取变更信息
            Map<String, String> placeholderMap = new HashMap<>();
            placeholderMap.put("NetworkFreezeLevel", info.getLevel().getInfo());
            placeholderMap.put("Requestor", String.format("%s(%s)", info.getRequester().getStaffName(), info.getRequester().getStaffId()));
            placeholderMap.put("NetworkFreezeURL", getFreezeURL(freezeRecordId, itemVo.getFreezeCode(), itemVo.getFreezeId()));
            placeholderMap.put("Current Date", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));


            String subject = replacePlaceholders(MailConstant.FREEZE_TODO_SUBJECT, placeholderMap);
            String content = replacePlaceholders(MailConstant.FREEZE_TODO_CONTENT, placeholderMap);

            // 处理固定收件人配置
            String[] recipients = processRecipients(to, cc);
            String finalTo = recipients[0];
            String finalCc = recipients[1];

            MailUtils.send(finalTo, finalCc, null, subject, content, true);

        } catch (Exception e) {
            log.error("发送封网邮件失败, ID: {}, UserID: {}", freezeInfoId, userId, e);
            throw new RuntimeException("发送封网邮件失败", e);
        }
    }

    // 通用URL构建方法
    private String buildJumpUrl(String type, Long id, Long recordId) {
        StringBuilder url = new StringBuilder(host)
            .append("/jumping?name=").append(type);

        if (id != null) {
            url.append("&fid=").append(id);
        }
        if (recordId != null) {
            url.append("&rid=").append(recordId);
        }

        return url.toString();
    }

    private String getFreezeURL(Long freezeRecordId, String freezeCode, Long freezeId) {
        String s = String.format("<a href='%s' target=\"_blank\">%s</a>",
            buildJumpUrl("FREEZE", freezeId, freezeRecordId), freezeCode);
        log.info("getFreezeURL: {}", s);
        return s;
    }

    /**
     * 获取变更URL
     *
     * @param changeRecordId 变更记录ID
     * @param changeCode     变更编号
     * @param changeId       变更ID
     * @return 变更URL
     */
    private String getChangeURL(Long changeRecordId, String changeCode, Long changeId) {
        String s = String.format("<a href='%s' target=\"_blank\">%s</a>",
            buildJumpUrl("CHANGE", changeId, changeRecordId), changeCode);
        log.info("getChangeURL: {}", s);
        return s;
    }

    private String getUserMail(Long userId) {
        return sysUserService.selectUserById(userId).getEmail();
    }
}
