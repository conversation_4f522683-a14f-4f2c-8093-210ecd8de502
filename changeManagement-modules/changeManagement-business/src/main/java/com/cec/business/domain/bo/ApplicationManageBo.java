package com.cec.business.domain.bo;

import com.cec.business.domain.ApplicationManage;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.StatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.domain.vo.FreezeAreaVo;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.system.domain.vo.UserVo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 应用管理业务对象 cm_application_manage
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@AutoMapper(target = ApplicationManage.class, reverseConvertGenerate = false)
public class ApplicationManageBo {

    /**
     *
     */
    @NotNull(message = "{id.not.null}", groups = { EditGroup.class })
    private Long id;

    /**
     * 应用名称
     */
    @NotBlank(message = "{app.name.not.blank}", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "{app.name.size}", groups = { AddGroup.class, EditGroup.class })
    private String applicationName;

    /**
     * BusinessOwner
     */
    @NotNull(message = "{app.business.owner.not.blank}", groups = { AddGroup.class, EditGroup.class })
    private UserVo businessOwner;

    /**
     * TeamLeader
     */
    @NotNull(message = "{app.team.leader.not.blank}", groups = { AddGroup.class, EditGroup.class })
    private UserVo teamLeader;

    /**
     * BusinessOwnerName
     */
    private String businessOwnerName;

    /**
     * TeamLeaderName
     */
    private String teamLeaderName;

    /**
     * Team_ID
     */
    private Long teamId;

    /**
     * Team_Name
     */
    private String teamName;

    /**
     * 分类id
     */
    @NotNull(message = "{app.category.id.not.null}", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 分类
     */
    @NotBlank(message = "{app.category.not.blank}", groups = { AddGroup.class, EditGroup.class })
    private String categoryName;

    /**
     * 核心项目（1-是 2-否）
     */
    private WhetherEnum isKeyProject;

    /**
     * 外部项目（1-是 2-否）
     */
    private WhetherEnum isExternalSystem;

    /**
     * 维护影响等级（1-一级 2-二级 3-三级)
     */
    private LevelEnum maintenanceLevel;

    /**
     * 状态（1-可用 2-非可用）
     */
    private StatusEnum status;

    /**
     * 简单检查表ids
     */
    private List<String> simpleCheckListIds;

    /**
     * 全量检查表ids
     */
    private List<String> fullCheckListIds;

    /**
     * 封网地区
     */
    private List<FreezeAreaVo> freezeAreaList;

}
